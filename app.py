from scrapegraphai.graphs import SmartScraperGraph
import json

# Configuración usando Groq (GRATIS con límites generosos)
graph_config = {
    "llm": {
        "model": "groq/llama3-8b-8192",  # Modelo Llama 3 8B en Groq
        "api_key": "TU_GROQ_API_KEY_AQUI",  # Reemplaza con tu API key de Groq
        "temperature": 0.1,
    },
    "verbose": True,
    "headless": True,
    "timeout": 30,
}

# Crear instancia del SmartScraperGraph
smart_scraper_graph = SmartScraperGraph(
    prompt="""Extrae información completa sobre ScrapegraphAI en español, incluyendo:
    1. Descripción de qué es y para qué sirve
    2. Instrucciones de instalación paso a paso
    3. Configuración básica y avanzada
    4. Ejemplos de código prácticos
    5. Diferentes tipos de scrapers disponibles
    6. Guías de uso y mejores prácticas
    7. Información sobre APIs y servicios
    8. Precios y planes disponibles""",
    source="https://scrapegraphai.com/",  # Página principal más confiable
    config=graph_config
)

print("🚀 Iniciando scraping con ScrapegraphAI + Groq...")
print("📄 Extrayendo información de:", smart_scraper_graph.source)

try:
    # Ejecutar el pipeline
    result = smart_scraper_graph.run()

    print("\n✅ Scraping completado exitosamente!")
    print("📊 Resultado:")
    print("=" * 80)
    print(json.dumps(result, indent=4, ensure_ascii=False))

    # Guardar resultado en archivo
    with open("scrapegraphai_info.json", "w", encoding="utf-8") as f:
        json.dump(result, f, indent=4, ensure_ascii=False)
    print(f"\n💾 Resultado guardado en: scrapegraphai_info.json")

except Exception as e:
    print(f"\n❌ Error durante el scraping: {e}")
    print("\n🔧 Posibles soluciones:")
    print("1. Verifica que tu API key de Groq sea válida")
    print("2. Asegúrate de tener créditos disponibles en Groq")
    print("3. Revisa tu conexión a internet")
    print("4. Intenta con una URL diferente")

    # Configuración alternativa con modelo más pequeño
    print("\n🔄 Intentando con configuración alternativa...")

    alternative_config = {
        "llm": {
            "model": "groq/llama3-70b-8192",  # Modelo más potente
            "api_key": "TU_GROQ_API_KEY_AQUI",
            "temperature": 0,
        },
        "verbose": False,
        "headless": True,
        "timeout": 20,
    }

    try:
        alternative_scraper = SmartScraperGraph(
            prompt="¿Qué es ScrapegraphAI y cuáles son sus características principales?",
            source="https://github.com/VinciGit00/Scrapegraph-ai",  # Fuente alternativa
            config=alternative_config
        )

        result = alternative_scraper.run()
        print("✅ Configuración alternativa exitosa!")
        print(json.dumps(result, indent=4, ensure_ascii=False))

    except Exception as e2:
        print(f"❌ Error en configuración alternativa: {e2}")
        print("\n📋 Para obtener una API key gratuita de Groq:")
        print("1. Ve a: https://console.groq.com/")
        print("2. Regístrate gratis")
        print("3. Ve a 'API Keys' y crea una nueva")
        print("4. Reemplaza 'TU_GROQ_API_KEY_AQUI' en este script")