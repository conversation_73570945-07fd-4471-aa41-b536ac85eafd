from scrapegraphai.graphs import SmartScraperGraph

# Define the configuration for the scraping pipeline usando Ollama (GRATIS)
graph_config = {
   "llm": {
       "model": "ollama/llama3.2",
       "base_url": "http://localhost:11434",  # URL por defecto de Ollama
   },
   "verbose": True,
   "headless": True,
   "timeout": 30,
}

# Create the SmartScraperGraph instance (usando SmartScraperGraph en lugar de Multi)
smart_scraper_graph = SmartScraperGraph(
    prompt="Extrae la documentación completa de ScrapegraphAI incluyendo: instalación, configuración, ejemplos de código, tipos de scrapers disponibles y guías de uso. Proporciona información detallada en español.",
    source="https://scrapegraph-ai.readthedocs.io/en/latest/getting_started/",
    config=graph_config
)

# Run the pipeline
result = smart_scraper_graph.run()

import json
print(json.dumps(result, indent=4))